<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Profile</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            /* Dark theme (space-themed) */
            --bg-color: #0a0a0a;
            --text-color: #ffffff;
            --container-bg: rgba(26, 26, 26, 0.8);
            --input-bg: rgba(64, 64, 64, 0.9);
            --button-bg: rgba(74, 144, 226, 0.8);
            --button-hover: rgba(123, 104, 238, 0.8);
            --secondary-text: #aaaaaa;
            --label-color: #dddddd;
            --link-color: #4a90e2;
            --box-shadow: rgba(0, 0, 0, 0.5);
            --notification-success: #28a745;
            --notification-error: #dc3545;
            --notification-info: #17a2b8;
            --notification-warning: #ffc107;
            --discord-color: #5865F2;
            --youtube-color: #FF0000;
            --card-bg: rgba(45, 45, 45, 0.9);
            --primary-color: #4a90e2;
            --secondary-color: #7b68ee;
            --accent-color: #ff6b6b;
            --nebula-color: rgba(123, 104, 238, 0.3);
            --star-color: #ffffff;
            --transition-speed: 0.3s;
        }

        /* Light theme (space-themed pinkish white) */
        :root.light-theme {
            --bg-color: #fff5f7;
            --text-color: #333333;
            --container-bg: rgba(255, 235, 238, 0.9);
            --input-bg: rgba(255, 205, 210, 0.8);
            --button-bg: rgba(248, 187, 208, 0.8);
            --button-hover: rgba(244, 143, 177, 0.8);
            --secondary-text: #757575;
            --label-color: #424242;
            --link-color: #ec407a;
            --box-shadow: rgba(0, 0, 0, 0.1);
            --notification-success: #66bb6a;
            --notification-error: #ef5350;
            --notification-info: #42a5f5;
            --notification-warning: #ffb74d;
            --discord-color: #5865F2;
            --youtube-color: #FF0000;
            --card-bg: rgba(255, 255, 255, 0.9);
            --primary-color: #ec407a;
            --secondary-color: #f48fb1;
            --accent-color: #ff4081;
            --nebula-color: rgba(244, 143, 177, 0.2);
            --star-color: #ec407a;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            transition: background-color var(--transition-speed) ease, color var(--transition-speed) ease;
        }

        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            background: radial-gradient(ellipse at center,
                var(--nebula-color) 0%,
                transparent 50%),
                var(--bg-color);
            color: var(--text-color);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
            transition: background var(--transition-speed) ease, color var(--transition-speed) ease;
        }

        /* Space background elements */
        .space-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            pointer-events: none;
        }

        .stars {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            pointer-events: none;
        }

        .star {
            position: absolute;
            background: var(--star-color);
            border-radius: 50%;
            animation: twinkle 2s infinite alternate;
            transition: background-color var(--transition-speed) ease;
        }

        .star.small {
            width: 1px;
            height: 1px;
        }

        .star.medium {
            width: 2px;
            height: 2px;
        }

        .star.large {
            width: 3px;
            height: 3px;
        }

        @keyframes twinkle {
            0% { opacity: 0.3; }
            100% { opacity: 1; }
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            margin-bottom: 30px;
        }

        .back-button {
            background: none;
            border: none;
            color: var(--text-color);
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            opacity: 0.8;
            transition: opacity 0.3s;
        }

        .back-button:hover {
            opacity: 1;
        }

        .theme-toggle {
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            font-size: 18px;
            opacity: 0.8;
            transition: opacity var(--transition-speed) ease;
            background-color: rgba(0,0,0,0.1);
            padding: 8px;
            border-radius: 50%;
        }

        .theme-toggle:hover {
            opacity: 1;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
        }

        .profile-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 40px;
        }

        .profile-pic-container {
            position: relative;
            width: 150px;
            height: 150px;
            margin-bottom: 20px;
        }

        .profile-pic {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: var(--button-bg);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            position: relative;
            border: 3px solid var(--button-hover);
        }

        .profile-pic img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .profile-pic .initial-avatar {
            font-size: 60px;
            font-weight: bold;
            color: var(--text-color);
        }

        .profile-pic-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s;
            cursor: pointer;
        }

        .profile-pic-container:hover .profile-pic-overlay {
            opacity: 1;
        }

        .profile-pic-overlay i {
            font-size: 40px;
            color: white;
        }

        .username {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .user-info {
            color: var(--secondary-text);
            margin-bottom: 20px;
        }

        .profile-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .profile-section {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px var(--box-shadow);
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: var(--label-color);
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--button-bg);
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 100px;
        }

        .save-btn {
            background-color: var(--notification-success);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
            transition: background-color 0.3s;
        }

        .save-btn:hover {
            opacity: 0.9;
        }

        .social-links {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .social-link {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            border-radius: 5px;
            background-color: var(--input-bg);
            transition: background-color 0.3s;
        }

        .social-link.connected {
            background-color: rgba(40, 167, 69, 0.2);
        }

        .social-link i {
            font-size: 24px;
            width: 30px;
            text-align: center;
        }

        .social-link .discord-icon {
            color: var(--discord-color);
        }

        .social-link .youtube-icon {
            color: #FF0000; /* YouTube red */
        }

        .social-link-info {
            flex-grow: 1;
        }

        .social-link-name {
            font-weight: bold;
        }

        .social-link-status {
            font-size: 12px;
            color: var(--secondary-text);
        }

        .social-link-button {
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 3px;
            background-color: var(--button-bg);
            transition: background-color 0.3s;
        }

        .social-link-button:hover {
            background-color: var(--button-hover);
        }

        .social-link-button.disconnect {
            background-color: var(--notification-error);
            color: white;
        }

        .social-link-button.disconnect:hover {
            opacity: 0.9;
        }

        .social-link-actions {
            display: flex;
            gap: 5px;
        }

        .social-link-button.visit {
            background-color: var(--youtube-color);
            color: white;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
        }

        .social-link-button.discord-visit {
            background-color: var(--discord-color);
        }

        .social-link-button.visit:hover {
            opacity: 0.9;
        }

        #notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 10px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            display: none;
        }

        #notification.success {
            background-color: var(--notification-success);
        }

        #notification.error {
            background-color: var(--notification-error);
        }

        #notification.info {
            background-color: var(--notification-info);
        }

        #file-input {
            display: none;
        }

        /* Modern file upload styling */
        .profile-upload-container {
            margin-top: 20px;
        }

        .file-upload-wrapper {
            display: flex;
            align-items: center;
            background-color: var(--input-bg);
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid var(--button-bg);
            margin-bottom: 8px;
        }

        .file-upload-info {
            flex: 1;
            padding: 12px 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            color: var(--secondary-text);
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .file-upload-info i {
            font-size: 18px;
            color: var(--secondary-text);
        }

        .file-upload-btn {
            background-color: var(--button-bg);
            color: var(--text-color);
            border: none;
            padding: 12px 20px;
            cursor: pointer;
            transition: background-color 0.3s;
            font-weight: 500;
            white-space: nowrap;
        }

        .file-upload-btn:hover {
            background-color: var(--button-hover);
        }

        .hidden-file-input {
            display: none;
        }

        .form-text {
            display: block;
            margin-top: 5px;
            font-size: 12px;
            color: var(--secondary-text);
        }

        /* Improved save button */
        .save-btn {
            background-color: var(--notification-success);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin-top: 20px;
            transition: all 0.3s;
            font-weight: 600;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .save-btn:hover {
            opacity: 0.9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .save-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 3px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .profile-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="space-background"></div>
    <div class="stars" id="stars"></div>

    <div id="notification"></div>
    <header>
        <a href="/" class="back-button">
            <i class="fas fa-arrow-left"></i> Back to Chat
        </a>
        <button class="theme-toggle" id="theme-toggle" title="Toggle Light/Dark Mode">
            <i class="fas fa-moon"></i>
        </button>
    </header>

    <div class="container">
        <div class="profile-header">
            <div class="profile-pic-container">
                <div class="profile-pic">
                    {% if session.profile_pic %}
                        <img src="{{ session.profile_pic }}" alt="Profile Picture" id="profile-image">
                    {% else %}
                        <span class="initial-avatar">{{ session.username[0] }}</span>
                    {% endif %}
                </div>
                <label for="file-input" class="profile-pic-overlay">
                    <i class="fas fa-camera"></i>
                </label>
                <input type="file" id="file-input" accept="image/*">
            </div>
            <h1 class="username">{{ session.username }}</h1>
            <p class="user-info">Member since {{ session.created_at|default('January 2023') }}</p>
        </div>

        <div class="profile-content">
            <div class="profile-section">
                <h2 class="section-title"><i class="fas fa-user"></i> Personal Information</h2>
                <form id="profile-form" action="/update_profile" method="POST" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="display-name">Display Name</label>
                        <input type="text" id="display-name" name="display_name" class="form-control" value="{{ session.username }}">
                    </div>
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" name="email" class="form-control" value="{{ session.email|default('') }}">
                    </div>
                    <div class="form-group">
                        <label for="birthdate">Birthdate</label>
                        <input type="date" id="birthdate" name="birthdate" class="form-control" value="{{ session.birthdate|default('') }}">
                    </div>
                    <div class="form-group">
                        <label for="hobbies">Hobbies</label>
                        <textarea id="hobbies" name="hobbies" class="form-control">{{ session.hobbies|default('') }}</textarea>
                    </div>
                    <div class="form-group profile-upload-container">
                        <label for="profile_pic">Profile Picture</label>
                        <div class="file-upload-wrapper">
                            <div class="file-upload-info">
                                <i class="fas fa-image"></i>
                                <span id="file-name">No file chosen</span>
                            </div>
                            <button type="button" class="file-upload-btn" onclick="document.getElementById('profile_pic').click()">Choose File</button>
                            <input type="file" id="profile_pic" name="profile_pic" accept="image/*" class="hidden-file-input">
                        </div>
                        <small class="form-text">You can also change your profile picture by clicking on it above.</small>
                    </div>
                    <button type="submit" class="save-btn">Save Changes</button>
                </form>
            </div>

            <div class="profile-section">
                <h2 class="section-title"><i class="fas fa-link"></i> Connected Accounts</h2>
                <div class="social-links">
                    <div class="social-link {% if session.discord_username %}connected{% endif %}">
                        <i class="fab fa-discord discord-icon"></i>
                        <div class="social-link-info">
                            <div class="social-link-name">Discord</div>
                            <div class="social-link-status">
                                {% if session.discord_username %}
                                    Connected as {{ session.discord_username }}
                                {% else %}
                                    Not connected
                                {% endif %}
                            </div>
                        </div>
                        {% if session.discord_username %}
                            <div class="social-link-actions">
                                <a href="https://discord.com/users/{{ session.discord_user_id }}" target="_blank" class="social-link-button visit discord-visit" title="Visit Discord Profile">
                                    <i class="fab fa-discord"></i>
                                </a>
                                <a href="/disconnect/discord" class="social-link-button disconnect">Disconnect</a>
                            </div>
                            <div class="code-display">
                                <div class="code-header">
                                    <span>Discord User ID</span>
                                    <button class="copy-btn" onclick="copyToClipboard('{{ session.discord_user_id }}')">
                                        <i class="fas fa-copy"></i> Copy
                                    </button>
                                </div>
                                <div class="code-content">
                                    <code>{{ session.discord_user_id }}</code>
                                </div>
                            </div>
                        {% else %}
                            <a href="/connect/discord" class="social-link-button connect">Connect</a>
                        {% endif %}
                    </div>

                    <div class="social-link {% if session.youtube_username %}connected{% endif %}">
                        <i class="fab fa-youtube youtube-icon"></i>
                        <div class="social-link-info">
                            <div class="social-link-name">YouTube</div>
                            <div class="social-link-status">
                                {% if session.youtube_username %}
                                    Connected as {{ session.youtube_username }}
                                {% else %}
                                    Not connected
                                {% endif %}
                            </div>
                        </div>
                        {% if session.youtube_username %}
                            <div class="social-link-actions">
                                <a href="https://www.youtube.com/channel/{{ session.youtube_channel_id }}" target="_blank" class="social-link-button visit" title="Visit YouTube Channel">
                                    <i class="fab fa-youtube"></i>
                                </a>
                                <a href="/disconnect/youtube" class="social-link-button disconnect">Disconnect</a>
                            </div>
                        {% else %}
                            <a href="/connect/youtube" class="social-link-button connect">Connect</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Theme toggle functionality
        document.getElementById('theme-toggle').addEventListener('click', function() {
            const root = document.documentElement;
            const themeIcon = this.querySelector('i');

            if (root.classList.contains('light-theme')) {
                // Switch to dark theme
                root.classList.remove('light-theme');
                themeIcon.classList.remove('fa-sun');
                themeIcon.classList.add('fa-moon');
                localStorage.setItem('theme', 'dark');
                showNotification('Dark mode activated', 'info');
            } else {
                // Switch to light theme
                root.classList.add('light-theme');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
                localStorage.setItem('theme', 'light');
                showNotification('Light mode (gray & white) activated', 'info');
            }
        });

        // Load theme preference on page load
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('theme-toggle');
            const themeIcon = themeToggle.querySelector('i');
            const root = document.documentElement;

            // Load theme preference
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'light') {
                root.classList.add('light-theme');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
            }

            // Profile picture upload via the overlay
            const fileInput = document.getElementById('file-input');
            fileInput.addEventListener('change', function(event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const profilePic = document.querySelector('.profile-pic');
                        profilePic.innerHTML = `<img src="${e.target.result}" alt="Profile Picture" id="profile-image">`;

                        // Also update the file input in the form
                        const formFileInput = document.getElementById('profile_pic');

                        // Create a new FileList-like object
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(file);
                        formFileInput.files = dataTransfer.files;
                    };
                    reader.readAsDataURL(file);
                    showNotification('Profile picture selected! Click Save Changes to update your profile.', 'info');
                }
            });

            // Preview profile picture when selected in the form
            const formFileInput = document.getElementById('profile_pic');
            formFileInput.addEventListener('change', function(event) {
                const file = event.target.files[0];
                if (file) {
                    // Update file name display
                    const fileNameElement = document.getElementById('file-name');
                    if (fileNameElement) {
                        fileNameElement.textContent = file.name;
                        fileNameElement.style.color = 'var(--text-color)';
                    }

                    // Preview the image
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const profilePic = document.querySelector('.profile-pic');
                        profilePic.innerHTML = `<img src="${e.target.result}" alt="Profile Picture" id="profile-image">`;
                    };
                    reader.readAsDataURL(file);
                }
            });

            // Flash messages
            const flashedMessagesElement = document.getElementById('flashed-messages-data');
            const flashedMessages = JSON.parse(flashedMessagesElement.dataset.messages || '[]');
            flashedMessages.forEach(([category, message]) => {
                showNotification(message, category);
            });

            // Create stars for space background
            createStars();
        });

        // Create stars function
        function createStars() {
            const starsContainer = document.getElementById('stars');
            const starCount = 100;

            for (let i = 0; i < starCount; i++) {
                const star = document.createElement('div');
                star.className = 'star';

                // Random size
                const sizes = ['small', 'medium', 'large'];
                const randomSize = sizes[Math.floor(Math.random() * sizes.length)];
                star.classList.add(randomSize);

                // Random position
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';

                // Random animation delay
                star.style.animationDelay = Math.random() * 2 + 's';

                starsContainer.appendChild(star);
            }
        }



        function showNotification(message, type) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = type; // success, error, or info
            notification.style.display = 'block';

            // Hide after 3 seconds
            setTimeout(() => {
                notification.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
    <div id="flashed-messages-data" data-messages="{{ get_flashed_messages(with_categories=true) | tojson }}"></div>
