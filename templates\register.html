<!DOCTYPE html>
<html>
<head>
    <title>Register - Voyager AI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* Dark theme (space-themed) */
            --bg-color: #0a0a0a;
            --text-color: #ffffff;
            --container-bg: rgba(26, 26, 26, 0.8);
            --input-bg: rgba(64, 64, 64, 0.9);
            --button-bg: rgba(74, 144, 226, 0.8);
            --button-hover: rgba(123, 104, 238, 0.8);
            --secondary-text: #aaaaaa;
            --label-color: #dddddd;
            --link-color: #4a90e2;
            --box-shadow: rgba(0, 0, 0, 0.5);
            --notification-success: #28a745;
            --notification-error: #dc3545;
            --notification-info: #17a2b8;
            --notification-warning: #ffc107;
            --primary-color: #4a90e2;
            --secondary-color: #7b68ee;
            --accent-color: #ff6b6b;
            --nebula-color: rgba(123, 104, 238, 0.3);
            --star-color: #ffffff;
            --transition-speed: 0.3s;
        }

        /* Light theme (space-themed pinkish white) */
        :root.light-theme {
            --bg-color: #fff5f7;
            --text-color: #333333;
            --container-bg: rgba(255, 235, 238, 0.9);
            --input-bg: rgba(255, 205, 210, 0.8);
            --button-bg: rgba(248, 187, 208, 0.8);
            --button-hover: rgba(244, 143, 177, 0.8);
            --secondary-text: #757575;
            --label-color: #424242;
            --link-color: #ec407a;
            --box-shadow: rgba(0, 0, 0, 0.1);
            --notification-success: #66bb6a;
            --notification-error: #ef5350;
            --notification-info: #42a5f5;
            --notification-warning: #ffb74d;
            --primary-color: #ec407a;
            --secondary-color: #f48fb1;
            --accent-color: #ff4081;
            --nebula-color: rgba(244, 143, 177, 0.2);
            --star-color: #ec407a;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            transition: background-color var(--transition-speed) ease, color var(--transition-speed) ease;
        }

        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            background: radial-gradient(ellipse at center,
                var(--nebula-color) 0%,
                transparent 50%),
                var(--bg-color);
            color: var(--text-color);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
            transition: background var(--transition-speed) ease, color var(--transition-speed) ease;
        }

        /* Space background elements */
        .space-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            pointer-events: none;
        }

        .stars {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            pointer-events: none;
        }

        .star {
            position: absolute;
            background: var(--star-color);
            border-radius: 50%;
            animation: twinkle 2s infinite alternate;
            transition: background-color var(--transition-speed) ease;
        }

        .star.small {
            width: 1px;
            height: 1px;
        }

        .star.medium {
            width: 2px;
            height: 2px;
        }

        .star.large {
            width: 3px;
            height: 3px;
        }

        @keyframes twinkle {
            0% { opacity: 0.3; }
            100% { opacity: 1; }
        }

        .auth-container {
            background: var(--container-bg);
            border-radius: 20px;
            padding: 40px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 20px 40px var(--box-shadow);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: background var(--transition-speed) ease, box-shadow var(--transition-speed) ease;
        }

        /* Theme toggle button */
        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: none;
            border: none;
            color: var(--text-color);
            cursor: pointer;
            font-size: 20px;
            opacity: 0.8;
            transition: opacity var(--transition-speed) ease;
        }

        .theme-toggle:hover {
            opacity: 1;
        }

        .auth-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .auth-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .auth-header p {
            color: var(--secondary-text);
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--label-color);
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 5px;
            background-color: var(--input-bg);
            color: var(--text-color);
            font-size: 16px;
        }

        .form-control:focus {
            outline: none;
            box-shadow: 0 0 0 2px #666666;
        }

        .btn {
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 5px;
            background-color: var(--button-bg);
            color: var(--text-color);
            font-size: 16px;
            cursor: pointer;
            transition: background-color var(--transition-speed);
        }

        .btn:hover {
            background-color: var(--button-hover);
        }

        .auth-footer {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            color: var(--secondary-text);
        }

        .auth-footer a {
            color: var(--link-color);
            text-decoration: none;
        }

        .auth-footer a:hover {
            text-decoration: underline;
        }

        .flash-messages {
            margin-bottom: 20px;
        }

        .flash-message {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .flash-message.error {
            background-color: var(--notification-error);
            color: white;
        }

        .flash-message.success {
            background-color: var(--notification-success);
            color: white;
        }

        .flash-message.info {
            background-color: var(--notification-info);
            color: white;
        }

        .remember-me {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .remember-me input[type="checkbox"] {
            margin-right: 8px;
            width: auto;
            cursor: pointer;
        }

        .remember-me label {
            margin-bottom: 0;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="space-background"></div>
    <div class="stars" id="stars"></div>

    <button class="theme-toggle" id="theme-toggle" title="Toggle Light/Dark Mode">
        <i class="fas fa-moon"></i>
    </button>
    <div class="auth-container">
        <div class="auth-header">
            <h1>Create an Account</h1>
            <p>Join Voyager AI and start chatting</p>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages">
                    {% for category, message in messages %}
                        <div class="flash-message {{ category }}">{{ message }}</div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <form method="POST" action="{{ url_for('register') }}">
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" class="form-control" required>
            </div>
            <div class="form-group">
                <label for="confirm_password">Confirm Password</label>
                <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
            </div>
            <div class="form-group remember-me">
                <input type="checkbox" id="remember" name="remember" value="1" checked>
                <label for="remember">Keep me logged in</label>
            </div>
            <button type="submit" class="btn">Register</button>
        </form>

        <div class="auth-footer">
            Already have an account? <a href="{{ url_for('login') }}">Login</a>
        </div>
    </div>
    <script>
        // Theme toggle functionality
        document.getElementById('theme-toggle').addEventListener('click', function() {
            const root = document.documentElement;
            const themeIcon = this.querySelector('i');

            if (root.classList.contains('light-theme')) {
                // Switch to dark theme
                root.classList.remove('light-theme');
                themeIcon.classList.remove('fa-sun');
                themeIcon.classList.add('fa-moon');
                localStorage.setItem('theme', 'dark');
            } else {
                // Switch to light theme
                root.classList.add('light-theme');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
                localStorage.setItem('theme', 'light');
            }
        });

        // Load theme preference on page load
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('theme-toggle');
            const themeIcon = themeToggle.querySelector('i');
            const root = document.documentElement;

            // Load theme preference
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'light') {
                root.classList.add('light-theme');
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
            }

            // Create stars for space background
            createStars();
        });

        // Create stars function
        function createStars() {
            const starsContainer = document.getElementById('stars');
            const starCount = 100;

            for (let i = 0; i < starCount; i++) {
                const star = document.createElement('div');
                star.className = 'star';

                // Random size
                const sizes = ['small', 'medium', 'large'];
                const randomSize = sizes[Math.floor(Math.random() * sizes.length)];
                star.classList.add(randomSize);

                // Random position
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';

                // Random animation delay
                star.style.animationDelay = Math.random() * 2 + 's';

                starsContainer.appendChild(star);
            }
        }
    </script>
</body>
</html>
