import json
import sqlite3
from datetime import datetime

def init_thread_db():
    """Initialize the threads table in the database"""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()

    # Create threads table if it doesn't exist
    c.execute('''
        CREATE TABLE IF NOT EXISTS threads
        (id INTEGER PRIMARY KEY AUTOINCREMENT,
         conversation_id INTEGER,
         thread_data TEXT,
         updated_at TIMESTAMP,
         FOREIGN KEY (conversation_id) REFERENCES conversations (id))
    ''')

    conn.commit()
    conn.close()

def save_thread(conversation_id, thread_data):
    """Save a thread to the database"""
    conn = sqlite3.connect('chats.db')
    # Set timeout to avoid database locks
    conn.execute('PRAGMA busy_timeout = 5000')
    # Use WAL mode for better concurrency
    conn.execute('PRAGMA journal_mode = WAL')
    c = conn.cursor()
    now = datetime.now()

    try:
        # Convert thread data to JSON string
        thread_json = json.dumps(thread_data)

        # Use UPSERT for better performance (SQLite 3.24.0 and higher)
        c.execute('''
            INSERT INTO threads (conversation_id, thread_data, updated_at)
            VALUES (?, ?, ?)
            ON CONFLICT(conversation_id)
            DO UPDATE SET thread_data = ?, updated_at = ?
        ''', (conversation_id, thread_json, now, thread_json, now))

        conn.commit()
        return True
    except sqlite3.OperationalError:
        # Fallback for older SQLite versions
        try:
            # Check if thread exists
            c.execute('SELECT id FROM threads WHERE conversation_id = ?', (conversation_id,))
            thread = c.fetchone()

            if thread:
                # Update existing thread
                c.execute('UPDATE threads SET thread_data = ?, updated_at = ? WHERE conversation_id = ?',
                        (thread_json, now, conversation_id))
            else:
                # Create new thread
                c.execute('INSERT INTO threads (conversation_id, thread_data, updated_at) VALUES (?, ?, ?)',
                        (conversation_id, thread_json, now))

            conn.commit()
            return True
        except Exception as e:
            print(f"Error in fallback save_thread: {str(e)}")
            conn.rollback()
            return False
    except Exception as e:
        print(f"Error saving thread: {str(e)}")
        conn.rollback()
        return False
    finally:
        conn.close()

def get_thread(conversation_id):
    """Get a thread from the database"""
    conn = sqlite3.connect('chats.db')
    # Set timeout to avoid database locks
    conn.execute('PRAGMA busy_timeout = 5000')
    # Use WAL mode for better concurrency
    conn.execute('PRAGMA journal_mode = WAL')
    c = conn.cursor()

    try:
        c.execute('SELECT thread_data FROM threads WHERE conversation_id = ?', (conversation_id,))
        thread = c.fetchone()

        if thread:
            try:
                # Parse JSON string back to Python object
                return json.loads(thread[0])
            except json.JSONDecodeError as e:
                print(f"Error decoding thread JSON: {str(e)}")
                return None
        return None
    except Exception as e:
        print(f"Error getting thread: {str(e)}")
        return None
    finally:
        conn.close()

def delete_thread(conversation_id):
    """Delete a thread from the database"""
    conn = sqlite3.connect('chats.db')
    c = conn.cursor()

    c.execute('DELETE FROM threads WHERE conversation_id = ?', (conversation_id,))

    conn.commit()
    conn.close()
