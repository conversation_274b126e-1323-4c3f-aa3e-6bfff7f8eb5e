# Environment Variables for VoyagerAI2

# Google Gemini API Key (Required for Vercel deployment)
# Get your API key from: https://makersuite.google.com/app/apikey
GOOGLE_API_KEY=your_google_api_key_here

# Flask Secret Key (Required for Vercel deployment)
# Generate a secure random key for session management
# You can generate one using: python -c "import secrets; print(secrets.token_hex(32))"
SECRET_KEY=your_secret_key_here

# Email Configuration (Optional - not used in current Vercel deployment)
GMAIL_USER=<EMAIL>
GMAIL_APP_PASSWORD=your_gmail_app_password_here

# Discord OAuth2 Settings (Optional - not used in current Vercel deployment)
DISCORD_CLIENT_ID=1363526079897276656
DISCORD_CLIENT_SECRET=PTQADje12sMwXBkb6SFLEYg2ulbR5XR6
DISCORD_REDIRECT_URI=http://localhost:5000/oauth/discord/callback

# YouTube OAuth2 Settings (Optional - not used in current Vercel deployment)
YOUTUBE_CLIENT_ID=768810287930-ah37v5fcsfuleqlifton7errine8hcia.apps.googleusercontent.com
YOUTUBE_CLIENT_SECRET=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
YOUTUBE_REDIRECT_URI=http://localhost:5000/oauth/youtube/callback
