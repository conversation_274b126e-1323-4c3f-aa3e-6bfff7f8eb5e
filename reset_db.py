#!/usr/bin/env python3

import os
import sqlite3

def reset_database():
    """Delete the existing database and recreate it with the new schema"""
    db_path = 'chats.db'
    
    # Delete existing database
    if os.path.exists(db_path):
        os.remove(db_path)
        print("Deleted existing database")
    
    # Import and initialize the new database
    from database import init_db
    init_db()
    print("Created new database with updated schema")
    
    # Test the new registration
    from database import register_user, get_user_by_unique_id
    
    print("\nTesting registration...")
    user_id, username = register_user('testuser', '<EMAIL>')
    if user_id:
        print(f"Successfully registered user: {username} with ID: {user_id}")
        
        # Test retrieval
        user = get_user_by_unique_id('testuser')
        if user:
            print(f"Successfully retrieved user: {user}")
        else:
            print("Failed to retrieve user")
    else:
        print("Failed to register user")

if __name__ == "__main__":
    reset_database()
