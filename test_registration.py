#!/usr/bin/env python3

import requests
import sys

def test_registration():
    """Test the registration endpoint"""
    url = "http://127.0.0.1:5000/register"

    # Test with valid data
    print("Testing registration with valid data...")
    test_data = {
        'username': 'newuser123',
        'email': '<EMAIL>',
        'password': 'password123',
        'confirm_password': 'password123'
    }
    response = requests.post(url, data=test_data)
    print(f"Status Code: {response.status_code}")
    if "Registration successful" in response.text:
        print("✅ Registration successful!")
    elif "Username already taken" in response.text:
        print("⚠️  Username already taken")
    else:
        print("❌ Unknown response")
        print("Response content (first 500 chars):")
        print(response.text[:500])

def test_login():
    """Test the login endpoint"""
    url = "http://127.0.0.1:5000/login"

    print("\nTesting login with email...")
    test_data = {
        'email': '<EMAIL>'
    }
    response = requests.post(url, data=test_data)
    print(f"Status Code: {response.status_code}")
    if "Login successful" in response.text:
        print("✅ Login successful!")
    elif "not found" in response.text.lower():
        print("⚠️  User not found")
    else:
        print("❌ Unknown response")

if __name__ == "__main__":
    test_registration()
    test_login()
